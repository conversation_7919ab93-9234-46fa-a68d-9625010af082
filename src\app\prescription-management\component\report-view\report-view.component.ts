import { CommonModule, NgFor } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import jsPDF from 'jspdf';
import { Reports } from '../../model/report.model';
import { SearchService } from '../../service/search.service';
import { HoldPrescription } from '../../model/hold-prescription.model';
import { HoldPrescriptionService } from '../../service/hold-prescription.service';
import { Alert, AlertService } from '../../service/alert.service';
import { AlertComponent } from '../alert/alert.component';

@Component({
  selector: 'app-report-view',
  standalone: true,
  imports: [CommonModule, AlertComponent],
  templateUrl: './report-view.component.html',
  styleUrls: ['./report-view.component.css'],
})
export class ReportViewComponent implements OnInit {
  public reportList: Reports[] = [];
  public isLoading: boolean = true; 
  public errorMessage: string | null = null; 
  public holdPatient: HoldPrescription[] = [];
  alerts: Alert[] = [];
  isModalOpen = true;

  constructor(
    private searchService: SearchService, 
    private holdPrecriptionService: HoldPrescriptionService,
    private alertService: AlertService 
  ) {}

  ngOnInit(): void {
    this.alertService.alert$.subscribe((alerts) => {
      this.alerts = alerts;
    });

    this.searchService.reportData$.subscribe((reportData: any)=>{
      this.reportList = reportData;
    });

  }

  async downloadPDF(report: Reports): Promise<void> {
    try {
      const doc = new jsPDF();
      const pageWidth = doc.internal.pageSize.width;
      const pageHeight = doc.internal.pageSize.height;
  
      const centerText = (text: string, y: number, size: number = 12, color: string = '#000000'): void => {
        doc.setFontSize(size);
        doc.setTextColor(color);
        const textWidth = doc.getTextWidth(text);
        const x = (pageWidth - textWidth) / 2;
        doc.text(text, x, y);
      };
  
      doc.setFillColor('#d4f3d4'); 
      doc.rect(0, 0, pageWidth, pageHeight, 'F'); 
  
      doc.setFont('helvetica', 'bold');
      doc.setFontSize(18);
      centerText('Medical Report', 15, 18, '#005500'); 
  
      doc.setDrawColor('#005500');
      doc.setLineWidth(0.5);
      doc.line(10, 20, pageWidth - 10, 20);
  
      doc.setFont('helvetica', 'normal');
      centerText(`Report ID: ${report.reportId}`, 35, 14, '#333333');
      centerText(`Patient ID: ${report.patientId}`, 45, 12, '#333333');
      centerText(`Category: ${report.categoryType}`, 55, 12, '#333333');
  
      doc.text(`Report Date: ${report.reportDate}`, 10, 70);
      doc.text(`Notes: ${report.note || 'N/A'}`, 10, 80);
  
      const footerText = 'Generated by We care';
      doc.setFontSize(10);
      doc.setTextColor('#333333');
      const footerWidth = doc.getTextWidth(footerText);
      doc.text(footerText, (pageWidth - footerWidth) / 2, pageHeight - 10);
  
      doc.save(`report-${report.reportId}.pdf`);
    } catch (error) {
      console.error('Error generating PDF:', error);
    }
  }

  // Hold Prescription Section
  getAllHoldPrescription(): void {
    this.holdPrecriptionService.getHoldPatientData().subscribe(
      (holdPrescriptions: HoldPrescription[]) => {
        this.holdPatient = holdPrescriptions;
      },
      (error) => {
        console.error('Error fetching hold prescriptions:', error);
      }
    );
  }

  resumePatient(holdPatient: HoldPrescription): void {
    this.searchService.getHoldPatientData(holdPatient);
    this.holdPrecriptionService.unholdPatientData(holdPatient.id!).subscribe(
      (next : string) => {
        this.alertService.showAlert('success', 'Patient data resumed successfully.');
        this.getAllHoldPrescription();
      },
      (error) => {
        this.alertService.showAlert('error', 'Unexpected error occurred.');
      }

    );
  }

  unHoldPatient(holdPatient: HoldPrescription): void {
    this.holdPrecriptionService.unholdPatientData(holdPatient.id!).subscribe(
      (next : string) => {
        this.alertService.showAlert('success', 'Patient data deleted successfully.'); 
        this.getAllHoldPrescription();
      },
      (error) => {
        this.alertService.showAlert('error', 'Unexpected error occurred.'); 
      }
    );
  }
  
}
